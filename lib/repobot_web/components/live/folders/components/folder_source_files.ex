defmodule RepobotWeb.Live.Folders.Components.FolderSourceFiles do
  use RepobotWeb, :live_component
  require Logger

  import RepobotWeb.UI.Components
  alias Repobot.{SourceFiles, SourceFile, Files}
  alias Repobot.Repo

  def mount(socket) do
    socket =
      socket
      |> assign(:show_edit_modal, false)
      |> assign(:editing_source_file, nil)
      |> assign(:show_diff_modal, false)
      |> assign(:diff_repository, nil)
      |> assign(:diff_source_file, nil)

    {:ok, socket}
  end

  def update(%{move_to_global_params: params} = assigns, socket) do
    # Handle forwarded move_to_global event
    {:noreply, new_socket} = handle_event("move_to_global", params, socket)
    {:ok, assign(new_socket, assigns)}
  end

  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end

  def render(assigns) do
    ~H"""
    <div>
      <!-- Main content -->
      <div class="px-6 py-4 border-b border-slate-200">
        <p class="text-sm text-slate-600">
          Source files that belong to this folder, grouped by their originating source repository
        </p>
      </div>
      <div class="divide-y divide-slate-200">
        <%= for {source_repository, source_files} <- group_source_files_by_repository(folder_source_files(@source_files, @folder), all_repositories(@folder), true) do %>
          <div class="border-b border-slate-100 last:border-b-0">
            <div class="px-6 py-4 border-b border-slate-100">
              <div class="flex items-center gap-2">
                <.icon name="hero-circle-stack" class="w-4 h-4 text-slate-400" />
                <h4 class="text-sm font-semibold text-slate-900">
                  <%= if source_repository do %>
                    <.link navigate={~p"/repositories/#{source_repository}"} class="link link-hover">
                      {source_repository.full_name}
                    </.link>
                    <%= if source_repository.template do %>
                      <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800">
                        Template
                      </span>
                    <% end %>
                  <% else %>
                    No source repository
                  <% end %>
                </h4>
              </div>
            </div>
            <%= for source_file <- source_files do %>
              <div class="pl-12 pr-6 py-4 group hover:bg-slate-50/50 transition-colors duration-150 border-l-2 border-transparent hover:border-indigo-200">
                <div class="flex items-center justify-between">
                  <div class="flex-1">
                    <div class="flex items-center gap-2">
                      <.icon name="hero-document-text" class="w-4 h-4 text-slate-400" />
                      <h3 class="text-sm font-medium text-slate-900">
                        <.link
                          navigate={~p"/source-files/#{source_file.id}"}
                          class="inline-flex items-center link link-hover"
                        >
                          {source_file.name}
                        </.link>
                      </h3>
                    </div>
                    <p class="mt-1 text-xs text-slate-500 ml-6">
                      {repositories_with_source_file(@folder.repositories, source_file)} of {length(
                        non_template_repositories(@folder.repositories)
                      )} repositories
                    </p>
                    <%= for pr <- Enum.filter(source_file.pull_requests, &(&1.status == "open")) do %>
                      <div class="mt-2">
                        <.pull_request_badge pull_request={pr} />
                      </div>
                    <% end %>
                    <%= if Map.get(source_file, :pr_results) do %>
                      <%= for {repo_name, result} <- source_file.pr_results do %>
                        <div class="mt-2">
                          <%= case result do %>
                            <% {:ok, "No changes needed - content is identical"} -> %>
                              <div class="flex items-center gap-2">
                                <.icon name="hero-check-circle" class="w-4 h-4 text-emerald-500" />
                                <span class="text-xs text-emerald-600">
                                  {repo_name} is up to date
                                </span>
                              </div>
                            <% {:ok, url} -> %>
                              <.pull_request_badge pull_request={
                                %{
                                  pull_request_url: url,
                                  pull_request_number: extract_pr_number(url)
                                }
                              } />
                            <% {:error, reason} -> %>
                              <div class="flex items-center gap-2">
                                <.icon name="hero-exclamation-triangle" class="w-4 h-4 text-red-500" />
                                <span class="text-xs text-red-600">
                                  Error for {repo_name}: {reason}
                                </span>
                              </div>
                          <% end %>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                  <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                    <%= unless source_file.read_only do %>
                      <.btn
                        phx-click="show_edit_modal"
                        phx-value-source_file_id={source_file.id}
                        phx-target={@myself}
                        variant="soft"
                        size="xs"
                      >
                        Edit
                      </.btn>
                    <% end %>
                    <%= if repositories_with_source_file(@folder.repositories, source_file) > 0 do %>
                      <.btn
                        phx-click="show_diff"
                        phx-value-source_file_id={source_file.id}
                        phx-value-repository={
                          get_first_repository_with_source_file(
                            @folder.repositories,
                            source_file
                          )
                        }
                        phx-target={@myself}
                        variant="soft"
                        size="xs"
                        title="Show diff with target repository"
                      >
                        <.icon name="hero-document-magnifying-glass" class="w-4 h-4" />
                      </.btn>
                    <% end %>
                    <.btn
                      phx-click="move_to_global"
                      phx-value-source_file_id={source_file.id}
                      phx-target={@myself}
                      variant="soft"
                      size="xs"
                    >
                      Move to Global
                    </.btn>
                    <%= if all_repositories_have_source_file?(@folder.repositories, source_file) do %>
                      <.btn
                        phx-click="remove_source_file"
                        phx-value-source_file_id={source_file.id}
                        phx-target={@myself}
                        variant="error"
                        size="xs"
                      >
                        Remove from All
                      </.btn>
                    <% else %>
                      <.btn
                        phx-click="add_source_file"
                        phx-value-source_file_id={source_file.id}
                        phx-target={@myself}
                        variant="primary"
                        size="xs"
                      >
                        Add to All
                      </.btn>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
        <%= if Enum.empty?(folder_source_files(@source_files, @folder)) do %>
          <div class="px-6 py-4 text-sm text-slate-500 italic">
            No source files belong to this folder yet.
          </div>
        <% end %>
      </div>
      
    <!-- Edit Modal -->
      <%= if @show_edit_modal do %>
        <div
          class="fixed inset-0 bg-slate-500/75 flex items-center justify-center z-50"
          phx-window-keydown="hide_edit_modal"
          phx-key="escape"
          phx-target={@myself}
        >
          <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col">
            <div class="px-6 py-4 border-b border-slate-200 flex items-center justify-between">
              <h3 class="text-lg font-medium text-slate-900">
                Edit Source File: {@editing_source_file.name}
              </h3>
              <.btn phx-click="hide_edit_modal" phx-target={@myself} variant="ghost" size="sm" circle>
                <.icon name="hero-x-mark" class="w-5 h-5" />
              </.btn>
            </div>
            <div class="p-6 overflow-auto flex-1">
              <form phx-submit="save_source_file" phx-target={@myself}>
                <div class="mb-4">
                  <label for="content" class="block text-sm font-medium text-slate-700 mb-2">
                    Content
                  </label>
                  <textarea
                    id="content"
                    name="content"
                    rows="20"
                    class="w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 font-mono text-sm"
                    placeholder="Enter source file content..."
                  >{Files.get_content(@editing_source_file)}</textarea>
                </div>
                <div class="flex justify-end gap-3">
                  <.btn type="button" phx-click="hide_edit_modal" phx-target={@myself} variant="soft">
                    Cancel
                  </.btn>
                  <.btn type="submit" variant="primary">
                    Save Changes
                  </.btn>
                </div>
              </form>
            </div>
          </div>
        </div>
      <% end %>

      <.live_component
        module={RepobotWeb.Live.Shared.DiffModal}
        id={"diff-modal-#{@myself.cid}"}
        show={@show_diff_modal}
        source_file={@diff_source_file}
        repository={@diff_repository}
        folder={@folder}
        on_hide={JS.push("hide_diff_modal", target: @myself)}
      />
    </div>
    """
  end

  # Event handlers with full logic encapsulation
  def handle_event("show_edit_modal", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id)

    {:noreply,
     socket
     |> assign(:show_edit_modal, true)
     |> assign(:editing_source_file, source_file)}
  end

  def handle_event("hide_edit_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_edit_modal, false)
     |> assign(:editing_source_file, nil)}
  end

  def handle_event("save_source_file", %{"content" => content}, socket) do
    case SourceFiles.update_source_file(socket.assigns.editing_source_file, %{content: content}) do
      {:ok, _source_file} ->
        # Notify parent to refresh source files
        send(self(), {:folder_source_files_event, "refresh_source_files", %{}})

        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: :info, message: "Source file updated successfully"}}
        )

        {:noreply,
         socket
         |> assign(:show_edit_modal, false)
         |> assign(:editing_source_file, nil)}

      {:error, _changeset} ->
        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: :error, message: "Failed to update source file"}}
        )

        {:noreply, socket}
    end
  end

  def handle_event(
        "show_diff",
        %{"source_file_id" => source_file_id, "repository" => repository},
        socket
      ) do
    # Use the DiffModal helper to prepare data
    diff_data =
      RepobotWeb.Live.Shared.DiffModal.prepare_diff_data(
        source_file_id,
        repository,
        socket.assigns.folder
      )

    {:noreply,
     socket
     |> assign(:show_diff_modal, true)
     |> assign(:diff_repository, diff_data.repository)
     |> assign(:diff_source_file, diff_data.source_file)}
  end

  def handle_event("hide_diff_modal", _, socket) do
    {:noreply,
     socket
     |> assign(:show_diff_modal, false)
     |> assign(:diff_repository, nil)
     |> assign(:diff_source_file, nil)}
  end

  def handle_event("move_to_global", %{"source_file_id" => id}, socket) do
    source_file = SourceFiles.get_source_file!(id, socket.assigns.current_organization.id)

    # Remove the source file from the current folder
    case SourceFiles.remove_source_file_from_folder(source_file, socket.assigns.folder) do
      {1, nil} ->
        # Notify parent to refresh source files and show success message
        send(self(), {:folder_source_files_event, "refresh_source_files", %{}})

        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: :info, message: "Source file moved to global"}}
        )

        {:noreply, socket}

      {0, nil} ->
        # Notify parent to show error message
        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: :error, message: "Failed to move source file to global"}}
        )

        {:noreply, socket}
    end
  end

  def handle_event("add_source_file", %{"source_file_id" => source_file_id}, socket) do
    case Repo.get(SourceFile, source_file_id) do
      nil ->
        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: :error, message: "Source file not found"}}
        )

        {:noreply, socket}

      source_file ->
        source_file = Repo.preload(source_file, [:category, :tags, :repositories, :pull_requests])

        results =
          socket.assigns.folder.repositories
          |> non_template_repositories()
          |> Enum.map(fn repository ->
            Repobot.Repositories.add_source_file(repository, source_file)
          end)

        success_count =
          Enum.count(results, fn
            {:ok, _} -> true
            _ -> false
          end)

        error_count =
          Enum.count(results, fn
            {:error, _} -> true
            _ -> false
          end)

        message =
          case {success_count, error_count} do
            {s, 0} -> "Successfully added source file to #{s} repositories"
            {0, e} -> "Failed to add source file to #{e} repositories"
            {s, e} -> "Added source file to #{s} repositories, failed for #{e} repositories"
          end

        # Notify parent to refresh folder data and show flash message
        send(self(), {:folder_source_files_event, "refresh_folder", %{}})

        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: if(error_count > 0, do: :error, else: :info), message: message}}
        )

        {:noreply, socket}
    end
  end

  def handle_event("remove_source_file", %{"source_file_id" => source_file_id}, socket) do
    case Repo.get(SourceFile, source_file_id) do
      nil ->
        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: :error, message: "Source file not found"}}
        )

        {:noreply, socket}

      source_file ->
        source_file = Repo.preload(source_file, [:category, :tags, :repositories, :pull_requests])

        results =
          Enum.map(socket.assigns.folder.repositories, fn repository ->
            Repobot.Repositories.remove_source_file(repository, source_file)
          end)

        success_count =
          Enum.count(results, fn
            {:ok, _} -> true
            _ -> false
          end)

        error_count =
          Enum.count(results, fn
            {:error, _} -> true
            _ -> false
          end)

        message =
          case {success_count, error_count} do
            {s, 0} -> "Successfully removed source file from #{s} repositories"
            {0, e} -> "Failed to remove source file from #{e} repositories"
            {s, e} -> "Removed source file from #{s} repositories, failed for #{e} repositories"
          end

        # Notify parent to refresh folder data and show flash message
        send(self(), {:folder_source_files_event, "refresh_folder", %{}})

        send(
          self(),
          {:folder_source_files_event, "flash_message",
           %{type: if(error_count > 0, do: :error, else: :info), message: message}}
        )

        {:noreply, socket}
    end
  end

  # Helper functions moved from parent
  defp folder_source_files(source_files, folder) do
    Enum.filter(source_files, fn source_file ->
      case source_file.folders do
        nil -> false
        folders -> Enum.any?(folders, fn sf_folder -> sf_folder.id == folder.id end)
      end
    end)
  end

  defp non_template_repositories(repositories) do
    repositories
    |> Enum.reject(& &1.template)
    |> Enum.sort_by(& &1.full_name)
  end

  defp all_repositories(folder) do
    (folder.repositories ++ folder.template_repositories)
    |> Enum.uniq_by(& &1.id)
  end

  defp group_source_files_by_repository(source_files, all_repositories, filter_by_folder) do
    # Create a map of repository_id -> repository for quick lookup from all available repositories
    repo_map = Map.new(all_repositories, fn repo -> {repo.id, repo} end)

    # Group source files by source_repository_id
    grouped =
      Enum.group_by(source_files, fn source_file ->
        source_file.source_repository_id
      end)

    # Convert to a list of {repository, source_files} tuples
    result =
      if filter_by_folder do
        # For folder source files: only include groups where the repository exists in the current folder or is nil
        grouped
        |> Enum.filter(fn {repo_id, _files} ->
          # Include if repo_id is nil (no source repository) or if the repository exists in the folder
          repo_id == nil or Map.has_key?(repo_map, repo_id)
        end)
      else
        # For global source files: include all groups, regardless of whether the repository exists in the folder
        grouped
      end

    result
    |> Enum.map(fn {repo_id, files} ->
      repository = if repo_id, do: Map.get(repo_map, repo_id), else: nil
      {repository, files}
    end)
    |> Enum.sort_by(fn {repo, _files} ->
      case repo do
        # Sort "No source repository" group last
        nil -> "zzz_no_source"
        repo -> repo.full_name
      end
    end)
  end

  defp all_repositories_have_source_file?(repositories, source_file) do
    non_template_repos = non_template_repositories(repositories)
    repositories_with_source_file(repositories, source_file) == length(non_template_repos)
  end

  defp repositories_with_source_file(repositories, source_file) do
    non_template_repos = non_template_repositories(repositories)

    Enum.count(non_template_repos, fn repo ->
      Enum.any?(repo.source_files || [], &(&1.id == source_file.id))
    end)
  end

  defp get_first_repository_with_source_file(repositories, source_file) do
    non_template_repos = non_template_repositories(repositories)

    case Enum.find(non_template_repos, fn repo ->
           Enum.any?(repo.source_files || [], &(&1.id == source_file.id))
         end) do
      nil -> nil
      repo -> repo.full_name
    end
  end

  # Helper to extract PR number from GitHub PR URL
  defp extract_pr_number(url) do
    case Regex.run(~r/\/pull\/(\d+)$/, url) do
      [_, number] -> String.to_integer(number)
      _ -> nil
    end
  end
end
